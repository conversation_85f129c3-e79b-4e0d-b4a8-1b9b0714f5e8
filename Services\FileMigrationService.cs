using Intra2025.Data;
using Intra2025.Models;
using Microsoft.EntityFrameworkCore;

namespace Intra2025.Services
{
    /// <summary>
    /// 檔案遷移服務，將不安全的檔案移動到安全目錄
    /// </summary>
    public class FileMigrationService
    {
        private readonly IWebHostEnvironment _environment;
        private readonly ILogger<FileMigrationService> _logger;
        private readonly ReportDbContext _reportContext;
        private readonly YCRSDbContext _ycrsContext;

        public FileMigrationService(
            IWebHostEnvironment environment,
            ILogger<FileMigrationService> logger,
            ReportDbContext reportContext,
            YCRSDbContext ycrsContext)
        {
            _environment = environment;
            _logger = logger;
            _reportContext = reportContext;
            _ycrsContext = ycrsContext;
        }

        /// <summary>
        /// 遷移所有不安全的檔案到安全目錄
        /// </summary>
        public async Task<MigrationResult> MigrateAllFilesAsync()
        {
            var result = new MigrationResult();

            try
            {
                _logger.LogInformation("開始檔案遷移作業");

                // 1. 遷移報告檔案
                var reportResult = await MigrateReportFilesAsync();
                result.ReportFilesMigrated = reportResult.FilesMigrated;
                result.ReportFilesErrors.AddRange(reportResult.Errors);

                // 2. 遷移 YCRS 檔案
                var ycrsResult = await MigrateYCRSFilesAsync();
                result.YCRSFilesMigrated = ycrsResult.FilesMigrated;
                result.YCRSFilesErrors.AddRange(ycrsResult.Errors);

                // 3. 清理空的上傳目錄
                await CleanupEmptyDirectoriesAsync();

                result.IsSuccess = true;
                result.TotalFilesMigrated = result.ReportFilesMigrated + result.YCRSFilesMigrated;

                _logger.LogInformation("檔案遷移完成。總共遷移 {TotalFiles} 個檔案", result.TotalFilesMigrated);

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "檔案遷移過程中發生錯誤");
                result.IsSuccess = false;
                result.ErrorMessage = "檔案遷移過程中發生錯誤。";
                return result;
            }
        }

        /// <summary>
        /// 遷移報告檔案
        /// </summary>
        private async Task<FileMigrationSubResult> MigrateReportFilesAsync()
        {
            var result = new FileMigrationSubResult();

            try
            {
                var reportFiles = await _reportContext.ELS_REPORT_FILES.ToListAsync();

                foreach (var reportFile in reportFiles)
                {
                    try
                    {
                        if (string.IsNullOrEmpty(reportFile.Filepath))
                            continue;

                        // 檢查是否已經在安全目錄中
                        if (reportFile.Filepath.StartsWith("/secure-uploads/"))
                            continue;

                        var oldPath = Path.Combine(_environment.WebRootPath, reportFile.Filepath.TrimStart('/'));
                        
                        if (!File.Exists(oldPath))
                        {
                            _logger.LogWarning("檔案不存在，跳過遷移: {FilePath}", oldPath);
                            continue;
                        }

                        // 生成新的安全路徑
                        var newSecurePath = GenerateSecureFilePath("Reports", reportFile.Filename ?? Path.GetFileName(oldPath));
                        var newPhysicalPath = Path.Combine(_environment.ContentRootPath, "App_Data", "SecureUploads", "Reports", Path.GetFileName(newSecurePath));

                        // 確保目錄存在
                        var directory = Path.GetDirectoryName(newPhysicalPath);
                        if (!Directory.Exists(directory))
                        {
                            Directory.CreateDirectory(directory!);
                        }

                        // 移動檔案
                        File.Move(oldPath, newPhysicalPath);

                        // 更新資料庫記錄
                        reportFile.Filepath = $"/secure-uploads/Reports/{Path.GetFileName(newSecurePath)}";
                        _reportContext.ELS_REPORT_FILES.Update(reportFile);

                        result.FilesMigrated++;
                        _logger.LogInformation("成功遷移報告檔案: {OldPath} -> {NewPath}", oldPath, newPhysicalPath);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "遷移報告檔案失敗: {FilePath}", reportFile.Filepath);
                        result.Errors.Add($"遷移報告檔案失敗: {reportFile.Filepath} - {ex.Message}");
                    }
                }

                await _reportContext.SaveChangesAsync();
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "遷移報告檔案過程中發生錯誤");
                result.Errors.Add($"遷移報告檔案過程中發生錯誤: {ex.Message}");
                return result;
            }
        }

        /// <summary>
        /// 遷移 YCRS 檔案
        /// </summary>
        private async Task<FileMigrationSubResult> MigrateYCRSFilesAsync()
        {
            var result = new FileMigrationSubResult();

            try
            {
                var ycrsFiles = await _ycrsContext.YCRS_Files.ToListAsync();

                foreach (var ycrsFile in ycrsFiles)
                {
                    try
                    {
                        if (string.IsNullOrEmpty(ycrsFile.Filepath))
                            continue;

                        // 檢查是否已經在安全目錄中
                        if (ycrsFile.Filepath.StartsWith("/secure-uploads/"))
                            continue;

                        var oldPath = Path.Combine(_environment.WebRootPath, ycrsFile.Filepath.TrimStart('/'));
                        
                        if (!File.Exists(oldPath))
                        {
                            _logger.LogWarning("檔案不存在，跳過遷移: {FilePath}", oldPath);
                            continue;
                        }

                        // 生成新的安全路徑
                        var newSecurePath = GenerateSecureFilePath("YCRS_Care", ycrsFile.FileName ?? Path.GetFileName(oldPath));
                        var newPhysicalPath = Path.Combine(_environment.ContentRootPath, "App_Data", "SecureUploads", "YCRS_Care", Path.GetFileName(newSecurePath));

                        // 確保目錄存在
                        var directory = Path.GetDirectoryName(newPhysicalPath);
                        if (!Directory.Exists(directory))
                        {
                            Directory.CreateDirectory(directory!);
                        }

                        // 移動檔案
                        File.Move(oldPath, newPhysicalPath);

                        // 更新資料庫記錄
                        ycrsFile.Filepath = $"/secure-uploads/YCRS_Care/{Path.GetFileName(newSecurePath)}";
                        _ycrsContext.YCRS_Files.Update(ycrsFile);

                        result.FilesMigrated++;
                        _logger.LogInformation("成功遷移 YCRS 檔案: {OldPath} -> {NewPath}", oldPath, newPhysicalPath);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "遷移 YCRS 檔案失敗: {FilePath}", ycrsFile.Filepath);
                        result.Errors.Add($"遷移 YCRS 檔案失敗: {ycrsFile.Filepath} - {ex.Message}");
                    }
                }

                await _ycrsContext.SaveChangesAsync();
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "遷移 YCRS 檔案過程中發生錯誤");
                result.Errors.Add($"遷移 YCRS 檔案過程中發生錯誤: {ex.Message}");
                return result;
            }
        }

        /// <summary>
        /// 清理空的上傳目錄
        /// </summary>
        private async Task CleanupEmptyDirectoriesAsync()
        {
            try
            {
                var uploadsPath = Path.Combine(_environment.WebRootPath, "uploads");
                
                if (Directory.Exists(uploadsPath))
                {
                    // 檢查目錄是否為空
                    var files = Directory.GetFiles(uploadsPath, "*", SearchOption.AllDirectories);
                    
                    if (files.Length == 0)
                    {
                        Directory.Delete(uploadsPath, true);
                        _logger.LogInformation("已清理空的上傳目錄: {UploadsPath}", uploadsPath);
                    }
                    else
                    {
                        _logger.LogWarning("上傳目錄仍有 {FileCount} 個檔案未遷移", files.Length);
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "清理上傳目錄時發生錯誤");
            }

            await Task.CompletedTask;
        }

        /// <summary>
        /// 生成安全的檔案路徑
        /// </summary>
        private string GenerateSecureFilePath(string category, string originalFileName)
        {
            var extension = Path.GetExtension(originalFileName);
            var nameWithoutExtension = Path.GetFileNameWithoutExtension(originalFileName);
            
            // 清理檔案名稱
            var cleanName = System.Text.RegularExpressions.Regex.Replace(nameWithoutExtension, @"[^\w\-_\.]", "_");
            
            // 限制長度
            if (cleanName.Length > 50)
            {
                cleanName = cleanName.Substring(0, 50);
            }
            
            // 添加時間戳確保唯一性
            var timestamp = DateTime.Now.ToString("yyyyMMddHHmmss");
            var uniqueId = Guid.NewGuid().ToString("N")[..8];
            
            return $"{timestamp}_{uniqueId}_{cleanName}{extension}";
        }

        /// <summary>
        /// 檢查遷移狀態
        /// </summary>
        public async Task<MigrationStatusResult> CheckMigrationStatusAsync()
        {
            var result = new MigrationStatusResult();

            try
            {
                // 檢查報告檔案
                var reportFiles = await _reportContext.ELS_REPORT_FILES.ToListAsync();
                result.TotalReportFiles = reportFiles.Count;
                result.MigratedReportFiles = reportFiles.Count(f => f.Filepath?.StartsWith("/secure-uploads/") == true);

                // 檢查 YCRS 檔案
                var ycrsFiles = await _ycrsContext.YCRS_Files.ToListAsync();
                result.TotalYCRSFiles = ycrsFiles.Count;
                result.MigratedYCRSFiles = ycrsFiles.Count(f => f.Filepath?.StartsWith("/secure-uploads/") == true);

                // 檢查舊目錄
                var uploadsPath = Path.Combine(_environment.WebRootPath, "uploads");
                if (Directory.Exists(uploadsPath))
                {
                    result.RemainingFilesInOldDirectory = Directory.GetFiles(uploadsPath, "*", SearchOption.AllDirectories).Length;
                }

                result.IsFullyMigrated = result.MigratedReportFiles == result.TotalReportFiles && 
                                       result.MigratedYCRSFiles == result.TotalYCRSFiles &&
                                       result.RemainingFilesInOldDirectory == 0;

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "檢查遷移狀態時發生錯誤");
                result.ErrorMessage = "檢查遷移狀態時發生錯誤。";
                return result;
            }
        }
    }

    /// <summary>
    /// 遷移結果
    /// </summary>
    public class MigrationResult
    {
        public bool IsSuccess { get; set; }
        public string ErrorMessage { get; set; } = string.Empty;
        public int TotalFilesMigrated { get; set; }
        public int ReportFilesMigrated { get; set; }
        public int YCRSFilesMigrated { get; set; }
        public List<string> ReportFilesErrors { get; set; } = new();
        public List<string> YCRSFilesErrors { get; set; } = new();
    }

    /// <summary>
    /// 子遷移結果
    /// </summary>
    public class FileMigrationSubResult
    {
        public int FilesMigrated { get; set; }
        public List<string> Errors { get; set; } = new();
    }

    /// <summary>
    /// 遷移狀態結果
    /// </summary>
    public class MigrationStatusResult
    {
        public int TotalReportFiles { get; set; }
        public int MigratedReportFiles { get; set; }
        public int TotalYCRSFiles { get; set; }
        public int MigratedYCRSFiles { get; set; }
        public int RemainingFilesInOldDirectory { get; set; }
        public bool IsFullyMigrated { get; set; }
        public string ErrorMessage { get; set; } = string.Empty;
    }
}
