using Intra2025.Data;
using Intra2025.Models.YouthCareReportService;
using Intra2025.Models;
using Microsoft.EntityFrameworkCore;

namespace Intra2025.Services
{
    /// <summary>
    /// 安全檔案下載服務，提供權限控制的檔案下載功能
    /// </summary>
    public class SecureFileDownloadService
    {
        private readonly IWebHostEnvironment _environment;
        private readonly ILogger<SecureFileDownloadService> _logger;
        private readonly YCRSDbContext _ycrsContext;
        private readonly ReportDbContext _reportContext;

        public SecureFileDownloadService(
            IWebHostEnvironment environment,
            ILogger<SecureFileDownloadService> logger,
            YCRSDbContext ycrsContext,
            ReportDbContext reportContext)
        {
            _environment = environment;
            _logger = logger;
            _ycrsContext = ycrsContext;
            _reportContext = reportContext;
        }

        /// <summary>
        /// 安全下載檔案
        /// </summary>
        public async Task<FileDownloadResult> DownloadFileAsync(string filePath, string userId, bool isAdmin, string userUnitCode)
        {
            var result = new FileDownloadResult();

            try
            {
                // 1. 驗證檔案路徑
                if (string.IsNullOrWhiteSpace(filePath))
                {
                    result.IsSuccess = false;
                    result.ErrorMessage = "檔案路徑無效。";
                    return result;
                }

                // 2. 防止路徑遍歷攻擊
                if (IsPathTraversalAttempt(filePath))
                {
                    _logger.LogWarning("Path traversal attempt detected: {FilePath} by user {UserId}", filePath, userId);
                    result.IsSuccess = false;
                    result.ErrorMessage = "檔案路徑包含不安全的字元。";
                    return result;
                }

                // 3. 驗證檔案權限
                var permissionResult = await ValidateFilePermissionAsync(filePath, userId, isAdmin, userUnitCode);
                if (!permissionResult.IsValid)
                {
                    result.IsSuccess = false;
                    result.ErrorMessage = permissionResult.ErrorMessage;
                    return result;
                }

                // 4. 取得實際檔案路徑
                var actualFilePath = GetActualFilePath(filePath);
                if (!File.Exists(actualFilePath))
                {
                    _logger.LogWarning("File not found: {FilePath} requested by user {UserId}", filePath, userId);
                    result.IsSuccess = false;
                    result.ErrorMessage = "檔案不存在。";
                    return result;
                }

                // 5. 檢查檔案大小
                var fileInfo = new FileInfo(actualFilePath);
                if (fileInfo.Length > 500 * 1024 * 1024) // 500MB 限制
                {
                    result.IsSuccess = false;
                    result.ErrorMessage = "檔案過大，無法下載。";
                    return result;
                }

                // 6. 讀取檔案內容
                var fileBytes = await File.ReadAllBytesAsync(actualFilePath);
                var fileName = Path.GetFileName(permissionResult.OriginalFileName ?? actualFilePath);
                var contentType = GetContentType(fileName);

                // 7. 記錄下載日誌
                _logger.LogInformation("File downloaded: {FileName} by user {UserId}", fileName, userId);

                result.IsSuccess = true;
                result.FileBytes = fileBytes;
                result.FileName = fileName;
                result.ContentType = contentType;

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error downloading file: {FilePath} by user {UserId}", filePath, userId);
                result.IsSuccess = false;
                result.ErrorMessage = "檔案下載過程中發生錯誤。";
                return result;
            }
        }

        /// <summary>
        /// 驗證檔案權限
        /// </summary>
        private async Task<FilePermissionResult> ValidateFilePermissionAsync(string filePath, string userId, bool isAdmin, string userUnitCode)
        {
            var result = new FilePermissionResult();

            try
            {
                // 檢查 YCRS 檔案權限
                var ycrsFile = await _ycrsContext.YCRS_Files
                    .FirstOrDefaultAsync(f => f.Filepath == filePath);

                if (ycrsFile != null)
                {
                    // 檢查 YCRS 記錄權限
                    var hasPermission = await ValidateYCRSFilePermissionAsync(ycrsFile.Id ?? "", userId, isAdmin, userUnitCode);
                    if (!hasPermission)
                    {
                        result.IsValid = false;
                        result.ErrorMessage = "您沒有權限下載此檔案。";
                        return result;
                    }

                    result.IsValid = true;
                    result.OriginalFileName = ycrsFile.FileName;
                    return result;
                }

                // 檢查報告檔案權限
                var reportFile = await _reportContext.ELS_REPORT_FILES
                    .FirstOrDefaultAsync(f => f.Filepath == filePath);

                if (reportFile != null)
                {
                    // 檢查報告權限
                    var hasPermission = await ValidateReportFilePermissionAsync(reportFile.Bbssno ?? "", userId, isAdmin, userUnitCode);
                    if (!hasPermission)
                    {
                        result.IsValid = false;
                        result.ErrorMessage = "您沒有權限下載此檔案。";
                        return result;
                    }

                    result.IsValid = true;
                    result.OriginalFileName = reportFile.Filename;
                    return result;
                }

                // 檔案不在資料庫中，拒絕存取
                result.IsValid = false;
                result.ErrorMessage = "檔案不存在或您沒有權限存取。";
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error validating file permission for {FilePath} by user {UserId}", filePath, userId);
                result.IsValid = false;
                result.ErrorMessage = "權限驗證過程中發生錯誤。";
                return result;
            }
        }

        /// <summary>
        /// 驗證 YCRS 檔案權限
        /// </summary>
        private async Task<bool> ValidateYCRSFilePermissionAsync(string recordId, string userId, bool isAdmin, string userUnitCode)
        {
            if (isAdmin) return true;

            // 檢查兒童記錄權限
            var childRecord = await _ycrsContext.YCRS_ChildRecord
                .FirstOrDefaultAsync(r => r.Id == recordId);
            if (childRecord != null)
            {
                return childRecord.CaseBelongId == userUnitCode;
            }

            // 檢查關懷記錄權限
            var careRecord = await _ycrsContext.YCRS_CareRecord
                .FirstOrDefaultAsync(r => r.Id == recordId);
            if (careRecord != null)
            {
                return careRecord.CaseBelongId == userUnitCode;
            }

            return false;
        }

        /// <summary>
        /// 驗證報告檔案權限
        /// </summary>
        private async Task<bool> ValidateReportFilePermissionAsync(string reportSno, string userId, bool isAdmin, string userUnitCode)
        {
            if (isAdmin) return true;

            var report = await _reportContext.ELS_REPORT
                .FirstOrDefaultAsync(r => r.Sno.ToString() == reportSno);

            if (report == null) return false;

            // 檢查是否為報告建立者或同部門
            return report.Postuserid == userId || report.Postdepartid == userUnitCode.Substring(0, 3);
        }

        /// <summary>
        /// 檢查是否為路徑遍歷攻擊
        /// </summary>
        private bool IsPathTraversalAttempt(string filePath)
        {
            var dangerousPatterns = new[]
            {
                "..",
                "~",
                "\\",
                "%2e%2e",
                "%2f",
                "%5c",
                "..%2f",
                "..%5c",
                "%252e%252e",
                "%c0%ae%c0%ae",
                "%c1%9c"
            };

            return dangerousPatterns.Any(pattern => 
                filePath.Contains(pattern, StringComparison.OrdinalIgnoreCase));
        }

        /// <summary>
        /// 取得實際檔案路徑
        /// </summary>
        private string GetActualFilePath(string relativePath)
        {
            // 移除開頭的斜線
            var cleanPath = relativePath.TrimStart('/');

            // 根據路徑類型決定實際位置
            if (cleanPath.StartsWith("secure-uploads/"))
            {
                // 新的安全上傳目錄
                var secureUploadPath = Path.Combine(_environment.ContentRootPath, "App_Data", "SecureUploads");
                var fileName = cleanPath.Substring("secure-uploads/".Length);
                return Path.Combine(secureUploadPath, fileName);
            }
            else if (cleanPath.StartsWith("uploads/"))
            {
                // 舊的上傳目錄（需要遷移）
                var legacyUploadPath = Path.Combine(_environment.WebRootPath, "uploads");
                var fileName = cleanPath.Substring("uploads/".Length);
                return Path.Combine(legacyUploadPath, fileName);
            }

            // 預設處理
            return Path.Combine(_environment.ContentRootPath, "App_Data", "SecureUploads", cleanPath);
        }

        /// <summary>
        /// 取得內容類型
        /// </summary>
        private string GetContentType(string fileName)
        {
            var extension = Path.GetExtension(fileName).ToLowerInvariant();
            return extension switch
            {
                ".pdf" => "application/pdf",
                ".zip" => "application/zip",
                ".7z" => "application/x-7z-compressed",
                ".odt" => "application/vnd.oasis.opendocument.text",
                ".ods" => "application/vnd.oasis.opendocument.spreadsheet",
                ".odp" => "application/vnd.oasis.opendocument.presentation",
                ".csv" => "text/csv",
                ".txt" => "text/plain",
                _ => "application/octet-stream"
            };
        }
    }

    /// <summary>
    /// 檔案下載結果
    /// </summary>
    public class FileDownloadResult
    {
        public bool IsSuccess { get; set; }
        public string ErrorMessage { get; set; } = string.Empty;
        public byte[] FileBytes { get; set; } = Array.Empty<byte>();
        public string FileName { get; set; } = string.Empty;
        public string ContentType { get; set; } = string.Empty;
    }

    /// <summary>
    /// 檔案權限驗證結果
    /// </summary>
    public class FilePermissionResult
    {
        public bool IsValid { get; set; }
        public string ErrorMessage { get; set; } = string.Empty;
        public string? OriginalFileName { get; set; }
    }
}
