﻿@page "/Adm_ReportList"
@using Intra2025.Components.Base
@using Intra2025.Models.Reports.Report
@using Intra2025.Services
@using Intra2025.Models
@using Intra2025.Data
@using Intra2025.Components.Layout
@using Microsoft.EntityFrameworkCore
@using static Intra2025.Services.SsoService
@* @layout EmptyLayout *@
@inject IConfiguration Configuration
@inject LaunchSettingsService LaunchSettings
@inject HttpClient Http
@inject ReportDbContext DbContext
@inherits BasePageComponent


@code {
    string all = "ALL";

}
<title>規劃報告書資料庫後台管理</title>

<div style="display: flex;justify-content: center;align-items: center;">
    <div class="loader"></div>
</div>

<div style="justify-content: center;align-items: center;width:90%">
    <div class="category-section">
        <button class="category-button" id="btnAll" @onclick="() => LoadReports(all)">全部 (@allReports.Where(r => r.State
                        == 1).Count())</button>
        @foreach (var category in categories)
        {
            <button class="category-button" @onclick="() => LoadReports(category.Key)">
                @category.Key (@category.Value)
            </button>
        }
    </div>
</div>

<div class="search-section">
    <div class="search-bar">
        <span>年度:</span><select @bind="selectedYear">
            <option value="">全部</option>
            @foreach (var year in years)
            {
                <option value="@year">@year</option>
            }
        </select>
    </div>
    <!-- 日期選擇 -->
    <div class="me-3" style="width:150px">
        <label for="startDate" class="form-label mb-0 fw-bold">上傳日期(起)：</label>
        <InputDate TValue="DateTime ?" @bind-Value="dt1" id="startDate" class="form-control" style="width: 140px;" />
    </div>
    <div class="me-3" style="width:150px">
        <label for="endDate" class="form-label mb-0 fw-bold">上傳日期(訖)：</label>
        <InputDate TValue="DateTime ?" @bind-Value="dt2" id="endDate" class="form-control" style="width: 140px;" />
    </div>
    <div class="search-bar">
        <input type="text" @bind="searchTerm" placeholder="搜尋計畫名稱或內容簡述" @onkeydown="HandleKeyDown">
        <button @onclick="Search">搜尋</button>
    </div>
    <button class="clear" @onclick="Clear">清除</button>

    <div style="display: inline-block;width:20%;margin-left:auto;color:#677ab1;font-weight:bolder">
        @_userState.UnitName：@_userState.UserName
    </div>
</div>


<button class="btn btn-primary me-3"
    style="background-color:cornflowerblue; border-radius: 10px; width:60px; height:30px;" @onclick="CreateNewRecord">
    新增
</button>

<div style="justify-content: center;align-items: center;">
    <table>
        <thead>
            <tr>
                <th style="width:5%">年度</th>
                <th style="width:10%">主管單位</th>
                <th style="width:60%">計畫名稱</th>
                <th style="width:15%">規劃階段期間</th>
                <th style="width:10%">維護</th>
            </tr>
        </thead>
        <tbody>
            @foreach (var report in pagedReports)
            {
                <tr>
                    <td>@report.PlanYear</td>
                    <td>@report.PowerDepart</td>
                    <td>
                        <span class="hover one" @onclick="() => ShowDetails(report)"><a> @report.Topic </a></span>
                    </td>
                    <td>@report.Begindate?.ToString("yyyy/M") ~ @report.Enddate?.ToString("yyyy/M")</td>
                    <td>
                        <button @onclick="() => OpenEditModal(report)">編輯</button>
                        <button @onclick="() => onDelete(report.Sno)">刪除</button>
                    </td>
                </tr>
            }
        </tbody>
    </table>

    <div class="pagination">
        <div class="pagination-controls">
            <button @onclick="PreviousPage" disabled="@(currentPage == 1)">上一頁</button>
            <span>第 @currentPage 頁</span><span>【共 @totalPages 頁】</span>
            <button @onclick="NextPage" disabled="@(currentPage == totalPages)">下一頁</button>
        </div>
    </div>
</div>

@if (showModal)
{
    <div class="modal">
        <div class="modalM-content">
            <span class="close" @onclick="CloseDetails">&times;</span>
            <h2>計畫詳細資訊</h2>
            <table class="detail-table">
                <tr>
                    <td style="width:15%">計畫編號</td>
                    <td>@selectedReport?.Sno </td>
                </tr>
                <tr>
                    <td style="width:15%">年度</td>
                    <td>@selectedReport?.PlanYear </td>
                </tr>
                <tr>
                    <td>主管單位 </td>
                    <td>@selectedReport?.PowerDepart</td>
                </tr>
                <tr>
                    <td>聯絡資訊</td>
                    <td>@selectedReport?.Tel</td>
                </tr>
                <tr>
                    <td>計畫名稱</td>
                    <td>@selectedReport?.Topic</td>
                </tr>
                <tr>
                    <td>規劃期間</td>
                    <td>@selectedReport?.Begindate?.ToString("yyyy/M")~@selectedReport?.Enddate?.ToString("yyyy/M")</td>
                </tr>
                <tr>
                    <td>計畫內容</td>
                    <td>@selectedReport?.Content</td>
                </tr>
                <tr>
                    <td>委託單位</td>
                    <td>@selectedReport?.Consign</td>
                </tr>
                <tr>
                    <td>經費</td>
                    <td>@selectedReport?.Fund 元</td>
                </tr>
                <tr>
                    <td>附檔下載</td>
                    <td>
                        @{
                            string fileKey = selectedReport?.Sno.ToString() ?? "";
                        }
                        @if (!string.IsNullOrEmpty(fileKey) && reportFiles.ContainsKey(fileKey))
                        {
                            string fileName = "";
                            int i = 1;
                            foreach (var file in reportFiles[fileKey])
                            {
                                if (file.Filedesc.Equals(""))
                                    fileName = file.Filename;
                                else
                                    fileName = file.Filedesc;
                                @(i.ToString() + ".")
                                <a href="@file.Filepath" style=" display: inline-block;padding-bottom: 10px;">@fileName</a>
                                i++;
                                <br />
                            }
                        }
                        else
                        {
                            <span>無附檔</span>
                        }
                    </td>
                </tr>
                <tr>
                    <td>紙本位置</td>
                    <td>@selectedReport?.PaperLocation</td>
                </tr>
                <tr>
                    <td>點閱數</td>
                    <td>@selectedReport?.Readcount </td>
                </tr>
            </table>
        </div>
    </div>

}



@if (DeleteShow)
{
    <div class="modal" tabindex="-1" role="dialog" style="display: @(DeleteShow ? "block" : "none")">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-body">
                    <p>確認要刪除此筆資料? </p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-primary" @onclick="() => DeleteReport(deleteSno)">確認</button>
                    <button type="button" class="btn btn-secondary" data-dismiss="modal"
                        @onclick="@(() => this.DeleteShow = false)">取消</button>
                </div>
            </div>
        </div>
    </div>
}

@code {
    public bool DeleteShow { get; set; } = false;

    private Dictionary<string, int> categories = new Dictionary<string, int>();
    private List<ELS_REPORTC> allReports = new List<ELS_REPORTC>();
    private List<ELS_REPORTC> filteredReports = new List<ELS_REPORTC>();
    private List<ELS_REPORTC> pagedReports = new List<ELS_REPORTC>();
    private List<int> years = new List<int>();
    private Dictionary<string, List<ELS_REPORT_FILEC>> reportFiles = new Dictionary<string, List<ELS_REPORT_FILEC>>();
    private string selectedCategory = "";
    private string searchTerm = "";
    private int currentPage = 1;
    private int pageSize = 10;
    private int totalPages => (int)Math.Ceiling((double)filteredReports.Count / pageSize);
    private string? apiBaseUrl;
    private int deleteSno;
    private DateTime? dt1, dt2; //上傳區間起訖


    private void onDelete(int id)
    {
        deleteSno = id;
        this.DeleteShow = true;
    }

    protected override async Task OnInitializedAsync()
    {
        apiBaseUrl = LaunchSettings.GetApplicationUrl();
        
        await base.OnInitializedAsync();

        // _httpContextAccessor.HttpContext.Session.SetString("UnitCode", "115");
        // DepName = "環保局";

        if (!_userState.DepCode.Substring(0, 3).Equals("113"))
        { //若不為管理單位「計畫處」，則只能看到自己單位的資料
            allReports = await DbContext.ELS_REPORT
            .Where(r => r.PlanYear.HasValue) // 篩選掉 PlanYear 為 NULL 的報告
            .Where(r => r.Postdepartid == _userState.DepCode.Substring(0, 3)) // 比對部門代碼 ex: 117
            .Where(r => !string.IsNullOrEmpty(r.Topic)) // 篩選掉 Topic 為 NULL 或空字串的值
            .Where(r => !dt1.HasValue || (r.Postdate.HasValue && r.Postdate.Value.Date >= dt1.Value.Date)) // 過濾上傳日期(起)
            .Where(r => !dt2.HasValue || (r.Postdate.HasValue && r.Postdate.Value.Date <= dt2.Value.Date)) // 過濾上傳日期(訖)
            .ToListAsync();
        }
        else
        {
            allReports = await DbContext.ELS_REPORT
            .Where(r => r.PlanYear.HasValue) // 篩選掉 PlanYear 為 NULL 的報告
            .Where(r => !string.IsNullOrEmpty(r.Topic)) // 篩選掉 Topic 為 NULL 或空字串的值
            .Where(r => !dt1.HasValue || (r.Postdate.HasValue && r.Postdate.Value.Date >= dt1.Value.Date)) // 過濾上傳日期(起)
            .Where(r => !dt2.HasValue || (r.Postdate.HasValue && r.Postdate.Value.Date <= dt2.Value.Date)) // 過濾上傳日期(訖)
            .ToListAsync();
        }

        years = allReports
        .Where(r => r.PlanYear.HasValue) // 篩選掉 NULL 值
        .Where(r => !string.IsNullOrEmpty(r.Topic)) // 篩選掉 Topic 為 NULL 或空字串的值
        .Where(r => !dt1.HasValue || (r.Postdate.HasValue && r.Postdate.Value.Date >= dt1.Value.Date)) // 過濾上傳日期(起)
        .Where(r => !dt2.HasValue || (r.Postdate.HasValue && r.Postdate.Value.Date <= dt2.Value.Date)) // 過濾上傳日期(訖)
        .Select(r => r.PlanYear ?? 0) // 將可空型別轉換為非可空型別
        .Distinct()
        .OrderByDescending(y => y)
        .ToList();

        //抓取報告檔
        var files = await DbContext.ELS_REPORT_FILES.ToListAsync();
        foreach (var file in files)
        {
            if (!reportFiles.ContainsKey(file.Bbssno!))
            {
                reportFiles[file.Bbssno!] = new List<ELS_REPORT_FILEC>();
            }
            reportFiles[file.Bbssno!].Add(file);
        }

        CalculateCategoryCounts();
        FilterReports();
    }


    private void CalculateCategoryCounts()
    {

        categories = allReports
        .Where(r => !string.IsNullOrEmpty(r.PowerDepart)) // 篩選掉 NULL 或空字串
        .Where(r => r.State == 1)
        .GroupBy(r => r.PowerDepart ?? "未知")
        .ToDictionary(g => g.Key, g => g.Count());
    }

    private void LoadReports(string category)
    {
        Console.WriteLine($"Loading reports for category: {category}");
        selectedCategory = category;
        currentPage = 1;
        FilterReports();
    }

    private void Search()
    {
        currentPage = 1;
        FilterReports();
    }

    private void Clear()
    {
        searchTerm = "";
        dt1 = null;
        dt2 = null;
        FilterReports();
    }

    private void FilterReports()
    {
        filteredReports = allReports
        .Where(r => string.IsNullOrEmpty(selectedCategory) || selectedCategory == "ALL" || r.PowerDepart == selectedCategory)
        .Where(r => string.IsNullOrEmpty(selectedYear) || r.PlanYear.ToString() == selectedYear)
        .Where(r => r.State == 1)
        .Where(r => string.IsNullOrEmpty(searchTerm) ||
        (!string.IsNullOrEmpty(r.Topic) && r.Topic != null && r.Topic.Contains(searchTerm, StringComparison.OrdinalIgnoreCase))
        ||
        (!string.IsNullOrEmpty(r.Content) && r.Content != null && r.Content.Contains(searchTerm,
        StringComparison.OrdinalIgnoreCase)))
        .Where(r => !dt1.HasValue || (r.Postdate.HasValue && r.Postdate.Value.Date >= dt1.Value.Date)) // 過濾上傳日期(起)
        .Where(r => !dt2.HasValue || (r.Postdate.HasValue && r.Postdate.Value.Date <= dt2.Value.Date)) // 過濾上傳日期(訖)
        .OrderByDescending(r => r.PlanYear)
        .ThenByDescending(r => r.Sno) // 然後按 bbssno 降序排序
        .ToList();

        Console.WriteLine($"Filtered reports count: {filteredReports.Count}");
        UpdatePagedReports();
    }

    private void UpdatePagedReports()
    {
        pagedReports = filteredReports
        .Skip((currentPage - 1) * pageSize)
        .Take(pageSize)
        .ToList();
        // Console.WriteLine($"Paged reports count: {pagedReports.Count}");
    }

    private void PreviousPage()
    {
        if (currentPage > 1)
        {
            currentPage--;
            UpdatePagedReports();
        }
    }

    private void NextPage()
    {
        if (currentPage < totalPages)
        {
            currentPage++;
            UpdatePagedReports();
        }
    }

    private string selectedYear
    {
        get => _selectedYear;
        set
        {
            if (_selectedYear != value)
            {
                _selectedYear = value;
                FilterReports();
            }
        }
    }

    private string _selectedYear = "";

    private bool showModal = false;
    private ELS_REPORTC? selectedReport;
    private string modalContent = string.Empty;

    private async Task ShowDetails(ELS_REPORTC report)
    {
        selectedReport = report;
        if (selectedReport != null)
        {
            selectedReport.Readcount = selectedReport.Readcount ?? 0; // 如果 Readcount為null，則設置為 0
            selectedReport.Readcount++;
            DbContext.ELS_REPORT.Update(selectedReport);
            await DbContext.SaveChangesAsync();
        }
        showModal = true;
    }

    [JSInvokable]
    private void CloseDetails()
    {
        showModal = false;
        StateHasChanged(); // Ensure the UI updates when the modal is closed
    }

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender)
        {
            // Register the .NET method to be callable from JavaScript
            await JS.InvokeVoidAsync("registerCloseDetails", DotNetObjectReference.Create(this));
        }
    }
    private void HandleKeyDown(KeyboardEventArgs args)
    {
        if (args.Key == "Enter")
        {
            Search();
        }
    }

    private async Task CreateNewRecord()
    {
        // 導航到新增記錄頁面
        NavigationManager.NavigateTo($"{Environment.GetEnvironmentVariable("ASPNETCORE_APPL_PATH")?.TrimEnd('/')}/Adm_ReportNewEdit/", true);
        await Task.CompletedTask;
    }

    //刪除特定資料
    private async Task DeleteReport(int id)
    {
        // var report = DbContext.ELS_REPORT
        // .Where(r => r.Sno == id)
        // .ExecuteDeleteAsync();
        // // 確認刪除後更新頁面
        // NavigationManager.NavigateTo("/Adm_ReportList", true);
        var report = await DbContext.ELS_REPORT
        .Where(r => r.Sno == id)
        .FirstOrDefaultAsync();

        if (report == null)
        {
            // 資料不存在，直接返回
            return;
        }

        // 權限檢查：只有資料所有者或管理員可以刪除
        bool canDelete = false;

        // 檢查是否為資料所有者
        if (report.Postuserid == _userState.Account)
        {
            canDelete = true;
        }
        // 檢查是否為管理員或特定管理部門（綜規科）
        else if (_userState.IsAdmin || _userState.DepCode == "113005")
        {
            canDelete = true;
        }

        if (canDelete)
        {
            DbContext.ELS_REPORT.Remove(report);
            await DbContext.SaveChangesAsync();

            // 確認刪除後更新頁面
            NavigationManager.NavigateTo("/Adm_ReportList", true);
        }
        else
        {
            await JS.InvokeAsync<object>("alert", "您沒有權限刪除此報告。只有資料所有者或管理員可以執行此操作。");
        }
    }

    private async Task OpenEditModal(ELS_REPORTC report)
    {
        // 權限檢查：只有資料所有者或管理員可以編輯
        bool canEdit = false;

        // 檢查是否為資料所有者
        if (report.Postuserid == _userState.Account)
        {
            canEdit = true;
        }
        // 檢查是否為管理員或特定管理部門（綜規科）
        else if (_userState.IsAdmin || _userState.DepCode == "113005")
        {
            canEdit = true;
        }

        if (canEdit)
        {
            NavigationManager.NavigateTo($"/Adm_ReportNewEdit/{report.Sno.ToString()}");
        }
        else
        {
            await JS.InvokeAsync<object>("alert", "您沒有權限編輯此報告。只有資料所有者或管理員可以執行此操作。");
        }
    }

}

<script>
    function registerCloseDetails(dotNetHelper) {
        window.closeModalFromJS = function () {
            dotNetHelper.invokeMethodAsync('CloseDetails');
        }
    }
</script>
