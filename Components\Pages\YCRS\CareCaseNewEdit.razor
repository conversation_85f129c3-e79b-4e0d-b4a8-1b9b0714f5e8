﻿@page "/CareCaseNewEdit"
@page "/CareCaseNewEdit/{ID?}"
@inject YCRSDbContext _context
@inject SecureFileUploadService SecureUploadService
@inject FileUploadConfigService UploadConfig
@using Intra2025.Components.Base
@using Intra2025.Data
@using Intra2025.Models
@using Intra2025.Services
@using Intra2025.Models.YouthCareReportService
@using Microsoft.AspNetCore.Components.Forms
@using System.Globalization
@using Microsoft.EntityFrameworkCore
@using System.Text.Json
@using static Intra2025.Services.SsoService
@inject EmailService EmailService
@inherits BasePageComponent
@using Microsoft.AspNetCore.WebUtilities
@using System.Web

<title>溫馨關懷表(資料編輯)</title>
<h1>溫馨關懷表</h1>
<h3 class="text-center mt-4">資料維護</h3>

<head>
    <link href="/modals.css" rel="stylesheet" />
</head>

<EditForm Model="CareRecord" OnValidSubmit="HandleValidSubmit" class="form-container">
    <DataAnnotationsValidator />

    <table class="form-table">
        <tr>
            <td><label for="Id">系統編號</label></td>
            <td>
                <span>@CareRecord.Id</span>
            </td>
        </tr>
        <tr>
            <td width="275px"><label for="BelongDate">案件所屬日期(月份)</label> <span
                    style="font-size: 12px; color: red;">&nbsp;*必填</span> </td>
            <td>
                <div>
                    <select @bind="SelectedYear" id="Year">
                        <option value="">選擇年份</option>
                        @foreach (var year in AvailableYears)
                        {
                            <option value="@year">@year</option>
                        }
                    </select>年
                    &nbsp;&nbsp;
                    <select @bind="SelectedMonth" id="Month">
                        <option value="">選擇月份</option>
                        @foreach (var month in AvailableMonths)
                        {
                            <option value="@month">@month</option>
                        }
                    </select>月
                </div>
            </td>
        </tr>
        <tr>
            <td><label for="Caregiver">照顧者</label> <span style="font-size: 12px; color: red;">&nbsp;*必填</span> </td>
            <td>
                <InputText @bind-Value="CareRecord.Caregiver" id="Caregiver" maxlength="50" />
                <ValidationMessage For="@(() => CareRecord.Caregiver)" class="validation-inline" />
            </td>
        </tr>
        <tr>
            <td><label for="AttributesId">個案類型</label> <span style="font-size: 12px; color: red;">&nbsp;*必填</span> </td>
            <td>
                <select @bind="CareRecord.AttributesId" class="form-select">
                    <option value="" disabled selected>請選擇</option> <!-- 預設選項 -->
                    @if (categories != null && categories.Any())
                    {
                        @foreach (var casebelong in categories)
                        {
                            <option value="@casebelong.Id">@casebelong.Name</option>
                        }
                    }
                    else
                    {
                        <option disabled>Loading belongs...</option>
                    }
                </select>
                @if (CareRecord.AttributesId?.Equals("B12") == true)
                {
                    <span>&nbsp; &nbsp; &nbsp;照顧人數: </span>
                    <InputText @bind-Value="CareRecord.NumberOfCare" style="width:30px" id="NumberOfCare" maxlength="2" />
                    <span>人</span>
                }
                <ValidationMessage For="@(() => CareRecord.AttributesId)" class="validation-inline" />

            </td>
        </tr>
        <tr>
            <td>
                <label for="Visitcare">是否訪視關懷</label><br />
                <span style="font-size:xx-small;color:deepskyblue">
                    打勾表示為「是」
                </span>
            </td>
            <td>
                <InputCheckbox @bind-Value="CareRecord.Visitcare" id="Visitcare" />
            </td>
        </tr>
        <tr>
            <td>
                <label for="FamilyMediation">是否須家事商談</label><br />
                <span style="font-size:xx-small;color:deepskyblue">
                    打勾表示為「是」
                </span>
            </td>
            <td>
                <InputCheckbox @bind-Value="CareRecord.FamilyMediation" id="FamilyMediation" />
            </td>
        </tr>
        <tr>
            <td><label for="Remarks">備註</label></td>
            <td> <textarea id="Remarks" class="form-control" style="width: 400px;height: 130px"
                    @bind="CareRecord.Remarks" maxlength="200" rows="5"></textarea></td>
        </tr>
        <tr>
            <td>
                溫馨關懷表上傳 <span style="font-size: 12px; color: red;">&nbsp;*必填</span> <br />
                <span style="font-size:xx-small;color:deepskyblue">
                    【支援的檔案格式為: @UploadConfig.GetAllowedExtensionsDisplay("YCRS_Care")】<br />
                    【只能上傳1件關懷表(1個檔),最大 @UploadConfig.GetFileSizeDisplay("YCRS_Care")】<br />
                    【檔案將進行安全掃描並儲存在安全目錄中】
                </span>
            </td>
            <td>
                <!-- 顯示資料庫中已有的檔案 -->
                @if (YCRS_files.TryGetValue(CareRecord.Id ?? "temp", out var dbFiles) && dbFiles.Any())
                {
                    <div>
                        <ul>
                            @foreach (var file in dbFiles)
                            {
                                <li>
                                    <a href="/api/secure-download?filePath=@(System.Net.WebUtility.UrlEncode(file.Filepath))" target="_blank">@file.FileName</a>
                                    <span class="btn btn-danger" @onclick="() => ConfirmDelete(file.Sno)">移除</span>
                                </li>
                            }
                        </ul>
                    </div>
                }

                <!-- 顯示用戶新選擇的檔案 -->
                @if (selectedFiles != null && selectedFiles.Any())
                {
                    <div>
                        <ul>
                            @foreach (var file in selectedFiles)
                            {
                                <li>@file.Name (@file.Size KB)</li>
                            }
                        </ul>
                    </div>
                }


                <!-- 檔案選擇 -->
                <InputFile OnChange="HandleFilesSelected" multiple />
            </td>


        </tr>
        <tr>
            <td>
                <label for="CreateDate">案件新增日期</label>
            </td>
            <td> <span>@CareRecord.CreateDate?.ToString("yyyy-MM-dd") </span> </td>
        </tr>
        <tr>
            <td>
                <label for="CaseBelongId">案件所屬戶所 </label>
            </td>
            <td> <span>@caseBelongName </span> &nbsp;<span> @(string.IsNullOrEmpty(CareRecord.CreateName) ? "" :
                                        $"(承辦人:{CareRecord.CreateName})") </span> </td>
        </tr>
    </table>

    <button type="submit" class="btn btn-warning btn-lg">@(_isEditMode ? "案件更新" : "新增案件")</button>

    <button type="button" class="btn btn-info btn-lg" @onclick="BackToList">回通報資料清單</button>
</EditForm>


<!-- 成功訊息 Modal -->
<div class="modal" tabindex="-1" role="dialog" style="display: @(UpdateSuccess ? "block" : "none")">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-body">
                <p>您的資料已成功更新！</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-primary" @onclick="CloseModal">確認</button>
            </div>
        </div>
    </div>
</div>

@if (SuccessMessage != null)
{
    <div class="alert alert-success mt-3 text-center">@SuccessMessage</div>
}

@code {
    [Parameter]
    public string? ID { get; set; } // 取得 URL 中的 id 參數
    private bool _isEditMode = false; //使用「新增」或「編輯」模式
    private string? caseBelongName = "";
    private string clientIp = "";
    // 檔案上傳限制現在由 FileUploadConfigService 管理
    private Dictionary<string, List<YCRS_Files>> YCRS_files = new Dictionary<string, List<YCRS_Files>>();
    private List<JsonDataLoader.Category>? categories = new();
    private List<JsonDataLoader.CaseBelong>? caseBelongs = new();
    private List<string>? recipients = new();
    private YCRS_CareRecord CareRecord = new YCRS_CareRecord();

    private string? SuccessMessage;



    private int deleteId;
    public bool DeleteShow { get; set; } = false;

    private bool UpdateSuccess { get; set; } = false;
    private int? SelectedYear { get; set; }
    private int? SelectedMonth { get; set; }

    private List<int> AvailableYears = Enumerable.Range(112, (DateTime.Now.Year - (1911 + 112) + 1))
    .Reverse() // 反轉順序
    .ToList();
    private List<int> AvailableMonths = Enumerable.Range(1, 12).ToList(); // 1 月到 12 月

    private string? searchTerm;
    private string? dt1Str;
    private string? dt2Str;
    private int? currentPage;

    private void ShowSuccessModal()
    {
        UpdateSuccess = true;
        StateHasChanged(); // 強制 UI 重新渲染
        NavigationManager.NavigateTo($"{Environment.GetEnvironmentVariable("ASPNETCORE_APPL_PATH")?.TrimEnd('/')}/CareCaseList");
    }

    private void CloseModal()
    {
        UpdateSuccess = false;
        StateHasChanged(); // 強制 UI 重新渲染
    }

    protected override async Task OnInitializedAsync()
    {
        Console.WriteLine($"ID={ID}");
        try
        {
            // 解析 QueryString
            var uri = NavigationManager.ToAbsoluteUri(NavigationManager.Uri);
            var query = QueryHelpers.ParseQuery(uri.Query);
            if (query.TryGetValue("searchTerm", out var qSearchTerm))
                searchTerm = qSearchTerm;
            if (query.TryGetValue("dt1", out var qDt1))
                dt1Str = qDt1;
            if (query.TryGetValue("dt2", out var qDt2))
                dt2Str = qDt2;
            if (query.TryGetValue("currentPage", out var qPage) && int.TryParse(qPage, out var parsedPage))
                currentPage = parsedPage;

            // 由 ID 判斷是否為編輯模式
            _isEditMode = !string.IsNullOrEmpty(ID);

            if (!string.IsNullOrEmpty(ID))
            {
                // 恢復隱藏的檔案（如果有）
                await RestoreHiddenFiles(ID);
            }

            // 1. 載入通用資料
            await LoadCommonData();

            // 2. 由是否有 ID 判断是否進入编輯模式
            if (_isEditMode)
            {
                // 编辑模式
                await LoadExistingRecord();
            }

            // 3. 强制更新 UI
            StateHasChanged();
        }
        catch (Exception ex)
        {
            SuccessMessage = $"初始化失败: {ex.Message}";
            Console.Error.WriteLine(ex);
        }
    }

    // 載入通用資料
    private async Task LoadCommonData()
    {
        // 抓取 Client IP
        clientIp = ClientIpService.ClientIp;

        // Loading JSON
        var jsonDataLoader = new JsonDataLoader(env);
        categories = await jsonDataLoader.LoadCategoriesFromJsonAsync("B");
        caseBelongs = await jsonDataLoader.LoadCaseBelongFromJsonAsync();
        recipients = await jsonDataLoader.LoadRecipientsFromJsonAsync();

        // Check JSON
        ValidateJsonFilePath();
    }

    // 验证 JSON 文件路径
    private void ValidateJsonFilePath()
    {
        var filePath = Path.Combine(env.WebRootPath, "data", "ChildCareKind.json");
        if (!File.Exists(filePath))
        {
            Console.WriteLine($"File not exit: {filePath}");
        }
    }

    // 新增資料
    private async Task CreateCareRecordAsync(YCRS_CareRecord careRecord)
    {
        if (!await ValidateRecordAsync(careRecord)) //進行必填欄位進行檢查
        {
            return;
        }

        try
        {

            if (!await ValidateRecordAsync(careRecord)) //進行必填欄位進行檢查
            {
                return;
            }

            string currentYearMonth = DateTime.Now.ToString("yyMM");

            // 獲取目前資料庫中最大序號的 Id
            string? nowMaxId = await _context.YCRS_CareRecord
            .AsNoTracking()
            .Where(record => record.Id != null && record.Id.StartsWith($"B{currentYearMonth}"))
            .OrderByDescending(record => record.Id)
            .Select(record => record.Id)
            .FirstOrDefaultAsync();

            // 設置新的流水號
            string newMaxId = string.IsNullOrEmpty(nowMaxId) || nowMaxId.Length != 8
            ? $"B{currentYearMonth}001"
            : $"B{currentYearMonth}{(int.Parse(nowMaxId.Substring(5, 3)) + 1):D3}";

            // 設定所屬月份
            careRecord.BelongDate = new DateTime((SelectedYear ?? 0) + 1911, (SelectedMonth ?? 1), 1);

            // 預設欄位值
            careRecord.CreateDate = DateTime.Now;
            careRecord.IsChecked = false;
            careRecord.IsMailed = false;
            careRecord.Id = newMaxId;
            careRecord.CaseBelongId = _userState.UnitCode;
            careRecord.CreateName = _userState.UserName;

            // 新增記錄到資料庫
            _context.YCRS_CareRecord.Add(careRecord);
            await _context.SaveChangesAsync();

            // 處理檔案
            var uploadDirectory = Path.Combine(Environment.CurrentDirectory, "wwwroot/uploads");
            if (!Directory.Exists(uploadDirectory))
            {
                Directory.CreateDirectory(uploadDirectory);
            }

            if (selectedFiles != null)
            {
                foreach (var file in selectedFiles)
                {
                    // 使用安全檔案上傳服務
                    var uploadResult = await SecureUploadService.UploadFileAsync(file, "YCRS_Care", _userState.Account);

                    if (!uploadResult.IsSuccess)
                    {
                        await JS.InvokeAsync<object>("alert", $"檔案 '{file.Name}' 上傳失敗: {uploadResult.ErrorMessage}");
                        return;
                    }

                    // 使用安全檔案上傳服務創建檔案記錄
                    var fileRecordCreated = await SecureUploadService.CreateYCRSFileRecordAsync(
                        careRecord.Id ?? "",
                        file.Name,
                        uploadResult.FilePath);

                    if (!fileRecordCreated)
                    {
                        await JS.InvokeAsync<object>("alert", $"檔案記錄創建失敗: {file.Name}");
                        return;
                    }
                }

                // 檔案記錄已在 SecureUploadService 中儲存，無需再次儲存
            }

            // 清空已選檔案
            selectedFiles = Array.Empty<IBrowserFile>();

            // 成功訊息
            Console.WriteLine($"案件 {careRecord.Id} 新增成功");
            await JS.InvokeAsync<object>("alert", $"案件 {careRecord.Id} 新增成功");

            NavigationManager.NavigateTo($"{Environment.GetEnvironmentVariable("ASPNETCORE_APPL_PATH")?.TrimEnd('/')}/CareCaseList");
        }
        catch (Exception ex)
        {
            // 處理例外
            Console.WriteLine($"新增案件失敗: {ex.Message}");
            throw; // 將例外重新拋出以便在 UI 上顯示錯誤
        }
    }

    // 提取現有紀錄
    private async Task LoadExistingRecord()
    {
        var record = await _context.YCRS_CareRecord.FindAsync(ID);
        Console.WriteLine(record == null ? "查無資料" : $"查到資料：{record.Id}");
        if (record == null)
        {
            // 如果記錄不存在，顯示錯誤訊息
            await JS.InvokeAsync<object>("alert", "無此案件資料");
            BackToList();
            return;
        }

        // 根據權限過濾單一記錄
        if (!_userState.IsAdmin && record.CaseBelongId != _userState.UnitCode)
        {
            // 如果是一般使用者且記錄不屬於該使用者的單位，顯示錯誤訊息
            await JS.InvokeAsync<object>("alert", "您無權存取該記錄");
            BackToList();
            return;
        }

        if (record != null)
        {
            CareRecord = record;

            // 所属日期
            HandleBelongDate();

            // 所属户所名稱
            LoadCaseBelongName();

            // 上傳文件
            await LoadUploadedFiles();
        }
        else
        {
            SuccessMessage = "找不到該筆資料";
        }
    }

    // 所属日期
    private void HandleBelongDate()
    {
        if (CareRecord?.BelongDate.HasValue == true)
        {
            var gregorianDate = CareRecord.BelongDate.Value;
            var taiwanYear = gregorianDate.Year - 1911; // 轉换為民國年
            SelectedYear = taiwanYear;
            SelectedMonth = gregorianDate.Month;
        }
    }

    // 取得戶所名稱
    private void LoadCaseBelongName()
    {
        var caseBelong = caseBelongs?.FirstOrDefault(c => c.Id == CareRecord?.CaseBelongId);
        if (caseBelong != null)
        {
            caseBelongName = caseBelong.Name ?? "";
        }
        else
            caseBelongName = _userState.UnitName;
    }

    // 讀取 上傳文件
    private async Task LoadUploadedFiles()
    {
        var files = await _context.YCRS_Files
        .Where(f => f.Id == ID)
        .ToListAsync();

        foreach (var file in files)
        {
            if (!YCRS_files.ContainsKey(file.Id?.ToString() ?? ""))
            {
                YCRS_files[file.Id?.ToString() ?? ""] = new List<YCRS_Files>();
            }
            YCRS_files[file.Id?.ToString() ?? ""].Add(file);
        }
    }

    private async Task HandleValidSubmit()
    {
        if (!_isEditMode)
        {
            await CreateCareRecordAsync(CareRecord);
        }
        else
        {
            await UpdateCareRecordAsync(CareRecord);
        }
    }

    private IBrowserFile[] selectedFiles = new IBrowserFile[0];

    private async Task HandleFilesSelected(InputFileChangeEventArgs e)
    {
        // 如果使用者選擇的檔案數量超過 1，直接 return
        if (e.FileCount > 1)
        {
            // 清空已上傳的檔案
            selectedFiles = Array.Empty<IBrowserFile>();
            // 可選：顯示提示訊息給使用者
            await JS.InvokeAsync<object>("alert", "最多只能上傳 1 個檔案");
            return;
        }

        // 限制最多上傳 1 個檔案
        selectedFiles = e.GetMultipleFiles(1).ToArray();
    }

    private void onDelete(int id)
    {
        deleteId = id;
        this.DeleteShow = true;
    }

    // 修改資料
    private async Task UpdateCareRecordAsync(YCRS_CareRecord careRecord)
    {
        try
        {
            // 驗證輸入資料
            if (careRecord == null)
            {
                await JS.InvokeAsync<object>("alert", "更新的案件資料不能為空");
                return;
            }

            // 判斷是否允許更新
            if (careRecord.IsChecked == true)
            {
                await JS.InvokeAsync<object>("alert", "此案件已由社會處【確認】(進行鎖定)，無法進行編輯");
                return;
            }

            if (!await ValidateRecordAsync(careRecord)) // 進行必填欄位進行檢查
            {
                return;
            }

            careRecord.BelongDate = new DateTime((SelectedYear ?? 0) + 1911, (SelectedMonth ?? 1), 1);

            // 查詢資料庫中是否存在此案件
            var existingRecord = await _context.YCRS_CareRecord
            .FirstOrDefaultAsync(r => r.Id == careRecord.Id);

            if (existingRecord == null)
            {
                await JS.InvokeAsync<object>("alert", "找不到要更新的案件資料");
                return;
            }

            // 查詢標記為刪除的檔案
            var markedForDeletion = await _context.YCRS_Files
            .Where(f => f.Id == careRecord.Id && f.IsMarkedForDeletion == true)
            .ToListAsync();

            // 檢查是否有上傳檔案或未被標記為刪除的檔案
            var existingFiles = await _context.YCRS_Files
            .Where(f => f.Id == careRecord.Id && !f.IsMarkedForDeletion)
            .ToListAsync();

            if (!existingFiles.Any() && (selectedFiles == null || !selectedFiles.Any()))
            {
                await JS.InvokeAsync<object>("alert", "請上傳溫馨關懷表檔案");
                return;
            }

            // 更新案件資料
            existingRecord.Caregiver = careRecord.Caregiver;
            existingRecord.AttributesId = careRecord.AttributesId;
            existingRecord.CaseBelongId = careRecord.CaseBelongId;
            existingRecord.NumberOfCare = careRecord.NumberOfCare;
            existingRecord.Visitcare = careRecord.Visitcare;
            existingRecord.FamilyMediation = careRecord.FamilyMediation;
            existingRecord.Remarks = careRecord.Remarks;
            existingRecord.BelongDate = careRecord.BelongDate;
            existingRecord.IsChecked = careRecord.IsChecked;

            // 確保檔案存放目錄存在
            var uploadDirectory = Path.Combine(Environment.CurrentDirectory, "wwwroot/uploads");
            if (!Directory.Exists(uploadDirectory))
            {
                Directory.CreateDirectory(uploadDirectory);
            }

            // 儲存上傳的檔案
            if (selectedFiles != null && selectedFiles.Any())
            {
                foreach (var file in selectedFiles)
                {
                    // 使用安全檔案上傳服務
                    var uploadResult = await SecureUploadService.UploadFileAsync(file, "YCRS_Care", _userState.Account);

                    if (!uploadResult.IsSuccess)
                    {
                        await JS.InvokeAsync<object>("alert", $"檔案 '{file.Name}' 上傳失敗: {uploadResult.ErrorMessage}");
                        return;
                    }

                    // 使用安全檔案上傳服務創建檔案記錄
                    var fileRecordCreated = await SecureUploadService.CreateYCRSFileRecordAsync(
                        careRecord.Id ?? "",
                        file.Name,
                        uploadResult.FilePath);

                    if (!fileRecordCreated)
                    {
                        await JS.InvokeAsync<object>("alert", $"檔案記錄創建失敗: {file.Name}");
                        return;
                    }
                }
            }

            // 刪除標記為刪除的檔案
            if (markedForDeletion.Any())
            {
                _context.YCRS_Files.RemoveRange(markedForDeletion);
            }

            // 儲存更改
            await _context.SaveChangesAsync();

            // 清空已選檔案
            selectedFiles = Array.Empty<IBrowserFile>();

            // 成功訊息
            await JS.InvokeAsync<object>("alert", $"案件 {careRecord.Id} 更新成功");
            BackToList();

            Console.WriteLine($"案件 {careRecord.Id} 更新成功");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"更新案件失敗: {ex.Message}");
            throw;
        }
    }

    // 標記檔案為刪除
    private async Task ConfirmDelete(int sno)
    {
        var confirmed = await JS.InvokeAsync<bool>("confirm", "您確定要移除此檔案嗎？");
        if (confirmed)
        {
            var file = await _context.YCRS_Files.FindAsync(sno);
            if (file != null && file.Id != null && YCRS_files.ContainsKey(file.Id))
            {
                file.IsMarkedForDeletion = true;
                await _context.SaveChangesAsync();

                // 更新 UI: 隱藏檔案
                YCRS_files[file.Id].Remove(file);
            }
        }
    }

    // 恢復隱藏檔案 (在使用者取消操作或頁面刷新時呼叫)
    private async Task RestoreHiddenFiles(string recordId)
    {
        var filesToRestore = await _context.YCRS_Files
        .Where(f => f.Id == recordId && f.IsMarkedForDeletion)
        .ToListAsync();

        foreach (var file in filesToRestore)
        {
            file.IsMarkedForDeletion = false;
        }

        await _context.SaveChangesAsync();
    }

    private void BackToList()
    {
        // 返回時帶回 QueryString
        var query = $"?searchTerm={HttpUtility.UrlEncode(searchTerm)}&dt1={dt1Str}&dt2={dt2Str}&currentPage={currentPage}";
        NavigationManager.NavigateTo($"{Environment.GetEnvironmentVariable("ASPNETCORE_APPL_PATH")?.TrimEnd('/')}/CareCaseList{query}",
        true);
    }

    //發送MAIL通知
    private async Task SendEmailNotification()
    {
        string subject = $"溫馨關懷表 ~ 【{CareRecord.Caregiver}案件】的資料登錄通知(案件編號:{CareRecord.Id})";
        string body = $"<p>【{CareRecord.Caregiver}案件】的資料登錄通知(案件編號:{CareRecord.Id})</p>";
        body = body + $"<p> 您所處理的案件，請至「員工入口網 / 業務系統(一) / 社福資訊專區 / 守護寶貝即時通」進行承辦。</p>";

        await EmailService.SendEmailAsync(recipients!, subject, body);
        Console.WriteLine("郵件發送成功");
    }

    private DateTime? GetBelongDate()
    {
        return SelectedYear.HasValue && SelectedMonth.HasValue
        ? new DateTime(SelectedYear.Value, SelectedMonth.Value, 1)
        : null;
    }
    private async Task<bool> ValidateRecordAsync(YCRS_CareRecord record) //進行必填欄位進行檢查
    {
        if (string.IsNullOrWhiteSpace(record.AttributesId))
        {
            await JS.InvokeAsync<object>("alert", "個案類型為必選欄位");
            return false;
        }

        if (!SelectedYear.HasValue || !SelectedMonth.HasValue)
        {
            await JS.InvokeAsync<object>("alert", "案件所屬月份為必填欄位");
            return false;
        }
        if (string.IsNullOrWhiteSpace(record.Caregiver))
        {
            await JS.InvokeAsync<object>("alert", "照顧者為必填欄位");
            return false;
        }

        if ((selectedFiles == null || !selectedFiles.Any()) && !(YCRS_files.TryGetValue(CareRecord.Id ?? "temp", out var
        dbFiles) && dbFiles.Any()))
        {
            await JS.InvokeAsync<object>("alert", "請務必上傳溫馨關懷表");
            return false;
        }

        return true;
    }
}