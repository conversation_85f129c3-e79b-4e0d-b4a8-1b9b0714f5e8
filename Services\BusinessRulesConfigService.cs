namespace Intra2025.Services
{
    /// <summary>
    /// 業務規則配置服務，集中管理所有業務規則設定
    /// </summary>
    public class BusinessRulesConfigService
    {
        /// <summary>
        /// 記錄可編輯的最大天數
        /// </summary>
        public int MaxEditableDays { get; set; } = 30;

        /// <summary>
        /// 記錄確認後是否允許取消確認
        /// </summary>
        public bool AllowUnconfirmAfterConfirm { get; set; } = true;

        /// <summary>
        /// 只有管理員可以取消確認
        /// </summary>
        public bool OnlyAdminCanUnconfirm { get; set; } = false;

        /// <summary>
        /// 刪除記錄前需要檢查相關資料
        /// </summary>
        public bool CheckRelatedDataBeforeDelete { get; set; } = true;

        /// <summary>
        /// 已確認的記錄是否可以刪除
        /// </summary>
        public bool AllowDeleteConfirmedRecords { get; set; } = false;

        /// <summary>
        /// 狀態變更是否需要原因說明
        /// </summary>
        public bool RequireReasonForStatusChange { get; set; } = false;

        /// <summary>
        /// 確認狀態時是否需要檢查必要文件
        /// </summary>
        public bool RequireDocumentsForConfirmation { get; set; } = true;

        /// <summary>
        /// 並發操作檢查是否啟用
        /// </summary>
        public bool EnableConcurrencyCheck { get; set; } = true;

        /// <summary>
        /// 詳細稽核日誌是否啟用
        /// </summary>
        public bool EnableDetailedAuditLog { get; set; } = true;

        /// <summary>
        /// 業務規則驗證是否啟用
        /// </summary>
        public bool EnableBusinessRuleValidation { get; set; } = true;

        /// <summary>
        /// 取得狀態變更的業務規則描述
        /// </summary>
        public string GetStatusChangeRuleDescription()
        {
            var rules = new List<string>();

            if (MaxEditableDays > 0)
            {
                rules.Add($"記錄建立超過 {MaxEditableDays} 天後，只有管理員可以變更狀態");
            }

            if (!AllowUnconfirmAfterConfirm)
            {
                rules.Add("已確認的記錄無法取消確認");
            }
            else if (OnlyAdminCanUnconfirm)
            {
                rules.Add("只有管理員可以取消已確認的狀態");
            }

            if (RequireReasonForStatusChange)
            {
                rules.Add("狀態變更需要提供原因說明");
            }

            return string.Join("；", rules);
        }

        /// <summary>
        /// 取得刪除操作的業務規則描述
        /// </summary>
        public string GetDeleteRuleDescription()
        {
            var rules = new List<string>();

            if (!AllowDeleteConfirmedRecords)
            {
                rules.Add("已確認的記錄無法刪除");
            }

            if (CheckRelatedDataBeforeDelete)
            {
                rules.Add("刪除前會檢查是否有相關聯的資料");
            }

            return string.Join("；", rules);
        }

        /// <summary>
        /// 驗證業務規則配置的合理性
        /// </summary>
        public ValidationResult ValidateConfiguration()
        {
            var result = new ValidationResult { IsValid = true };

            if (MaxEditableDays < 0)
            {
                result.IsValid = false;
                result.ErrorMessage = "最大可編輯天數不能為負數";
                return result;
            }

            if (OnlyAdminCanUnconfirm && !AllowUnconfirmAfterConfirm)
            {
                result.IsValid = false;
                result.ErrorMessage = "配置衝突：不允許取消確認但又設定只有管理員可以取消確認";
                return result;
            }

            return result;
        }
    }

    /// <summary>
    /// 業務規則違反異常
    /// </summary>
    public class BusinessRuleViolationException : Exception
    {
        public string RuleName { get; }
        public string RuleDescription { get; }

        public BusinessRuleViolationException(string ruleName, string ruleDescription) 
            : base($"業務規則違反: {ruleName} - {ruleDescription}")
        {
            RuleName = ruleName;
            RuleDescription = ruleDescription;
        }

        public BusinessRuleViolationException(string ruleName, string ruleDescription, Exception innerException) 
            : base($"業務規則違反: {ruleName} - {ruleDescription}", innerException)
        {
            RuleName = ruleName;
            RuleDescription = ruleDescription;
        }
    }
}
